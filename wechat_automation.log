2025-08-05 21:36:47,520 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 21:36:47,527 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 21:36:47,533 - INFO - ================================================================================
2025-08-05 21:36:47,533 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 21:36:47,533 - INFO - ================================================================================
2025-08-05 21:36:47,533 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 21:36:47,534 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 21:36:47,534 - INFO - 执行时间: 2025-08-05 21:36:47
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 启动全新自动化爬取流程...
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 21:36:48,077 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 21:36:48,078 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 21:36:48,078 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 21:36:48,078 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 21:36:48,078 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 21:36:48,079 - INFO - ============================================================
2025-08-05 21:36:48,079 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 21:36:48,079 - INFO - ============================================================
2025-08-05 21:36:48,079 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 21:36:48,080 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:36:48,080 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:36:48,080 - INFO - === 开始重置网络状态 ===
2025-08-05 21:36:48,080 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:36:48,178 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:36:48,179 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:36:48,179 - INFO - 系统代理已成功关闭
2025-08-05 21:36:48,179 - INFO - ✅ 代理关闭操作
2025-08-05 21:36:48,179 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:36:48,380 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:36:48,396 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:36:48,396 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:36:48,396 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:36:48,396 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:36:51,111 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:36:51,111 - INFO - 🔄 Cookie抓取器进程已启动，PID: 22624
2025-08-05 21:36:51,112 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:36:54,112 - INFO - 等待代理服务启动...
2025-08-05 21:36:54,114 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:36:54,200 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:36:54,200 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 22624)
2025-08-05 21:36:54,201 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:36:54,201 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 21:36:54,202 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:36:54,202 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:36:54,202 - INFO - 正在查找微信主窗口...
2025-08-05 21:36:54,314 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:36:54,314 - INFO - 正在激活微信窗口...
2025-08-05 21:36:56,832 - INFO - 微信窗口已激活。
2025-08-05 21:36:56,833 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:37:03,421 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 21:37:03,422 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 21:37:06,752 - INFO - 正在查找聊天输入框...
2025-08-05 21:37:08,753 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 21:37:08,763 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 21:37:08,763 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 21:37:10,341 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 21:37:12,626 - INFO - 链接已粘贴，正在发送...
2025-08-05 21:37:12,909 - INFO - 找到发送按钮，点击发送...
2025-08-05 21:37:13,721 - INFO - 链接已发送。
2025-08-05 21:37:16,723 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 21:37:18,808 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 21:37:19,538 - INFO - ✅ 成功点击最新链接。
2025-08-05 21:37:22,538 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 21:37:22,539 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:22,539 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:22,556 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:25,260 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:26,761 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:37:40,038 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 21:37:40,038 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 21:37:40,038 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 21:37:40,038 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 21:37:40,038 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 21:37:40,038 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:40,038 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:40,041 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:42,746 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:43,247 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:43,248 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:43,257 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:45,963 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:47,464 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:38:00,369 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 21:38:00,370 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 21:38:02,871 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:02,872 - INFO - 🎉 检测到抓包成功！在第 1 次刷新后停止
2025-08-05 21:38:02,872 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 21:38:02,873 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:02,873 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 21:38:02,874 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 21:38:02,875 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 21:38:02,875 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 21:38:03,876 - INFO - 检测到Cookie文件已生成。
2025-08-05 21:38:03,876 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:03,876 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 21:38:03,876 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 21:38:03,876 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 21:38:03,877 - INFO - 正在停止Cookie抓取器 (PID: 22624)...
2025-08-05 21:38:03,878 - INFO - Cookie抓取器已成功终止。
2025-08-05 21:38:03,878 - INFO - 正在验证并清理代理设置...
2025-08-05 21:38:03,878 - INFO - === 开始重置网络状态 ===
2025-08-05 21:38:03,878 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:38:03,968 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:38:03,968 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:38:03,968 - INFO - 系统代理已成功关闭
2025-08-05 21:38:03,968 - INFO - ✅ 代理关闭操作
2025-08-05 21:38:03,969 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:38:04,030 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:38:04,030 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:38:04,030 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 21:38:04,112 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:38:04,113 - INFO - ✅ 网络连接验证正常
2025-08-05 21:38:07,113 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 21:38:07,114 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 21:38:07,114 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 21:38:07,381 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 21:42:23,928 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 21:42:23,929 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 21:42:23,933 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 21:42:23,934 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 21:42:23,934 - INFO - 执行时间: 2025-08-05 21:42:23
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 启动全新自动化爬取流程...
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 21:42:23,935 - INFO - ================================================================================
2025-08-05 21:42:23,935 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 21:42:24,164 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 21:42:24,165 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 21:42:24,165 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 21:42:24,165 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 21:42:24,165 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 21:42:24,165 - INFO - ============================================================
2025-08-05 21:42:24,165 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 21:42:24,165 - INFO - ============================================================
2025-08-05 21:42:24,165 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 21:42:24,165 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:42:24,166 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:42:24,166 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:42:24,166 - INFO - === 开始重置网络状态 ===
2025-08-05 21:42:24,166 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:42:24,260 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:42:24,260 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:42:24,260 - INFO - 系统代理已成功关闭
2025-08-05 21:42:24,260 - INFO - ✅ 代理关闭操作
2025-08-05 21:42:24,260 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:42:24,354 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:42:24,355 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:42:24,355 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:42:24,355 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:42:24,356 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:42:26,036 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:42:26,036 - INFO - 🔄 Cookie抓取器进程已启动，PID: 16812
2025-08-05 21:42:26,037 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:42:29,037 - INFO - 等待代理服务启动...
2025-08-05 21:42:29,038 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:42:29,059 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:42:29,059 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 16812)
2025-08-05 21:42:29,059 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:42:29,059 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 21:42:29,060 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:42:29,060 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:42:29,060 - INFO - 正在查找微信主窗口...
2025-08-05 21:42:30,215 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:42:30,216 - INFO - 正在激活微信窗口...
2025-08-05 21:42:32,750 - INFO - 微信窗口已激活。
2025-08-05 21:42:32,751 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:42:39,435 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 21:42:39,436 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 21:42:42,768 - INFO - 正在查找聊天输入框...
2025-08-05 21:42:44,770 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 21:42:44,778 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 21:42:44,778 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 21:42:46,370 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 21:42:48,655 - INFO - 链接已粘贴，正在发送...
2025-08-05 21:42:48,900 - INFO - 找到发送按钮，点击发送...
2025-08-05 21:42:49,701 - INFO - 链接已发送。
2025-08-05 21:42:52,702 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 21:42:54,796 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 21:42:55,521 - INFO - ✅ 成功点击最新链接。
2025-08-05 21:42:58,521 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 21:42:58,522 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:42:58,522 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:42:58,530 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:43:01,237 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:43:02,738 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:43:14,996 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 21:43:14,996 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 21:43:14,996 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 21:43:14,996 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 21:43:14,996 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:14,996 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 21:43:14,996 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 21:43:14,997 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:14,997 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 21:43:14,997 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 21:43:14,997 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 21:43:14,997 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 21:43:15,997 - INFO - 检测到Cookie文件已生成。
2025-08-05 21:43:15,998 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:15,999 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 21:43:15,999 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 21:43:16,000 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 21:43:16,000 - INFO - 正在停止Cookie抓取器 (PID: 16812)...
2025-08-05 21:43:16,002 - INFO - Cookie抓取器已成功终止。
2025-08-05 21:43:16,002 - INFO - 正在验证并清理代理设置...
2025-08-05 21:43:16,002 - INFO - === 开始重置网络状态 ===
2025-08-05 21:43:16,003 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:43:16,130 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:43:16,130 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:43:16,131 - INFO - 系统代理已成功关闭
2025-08-05 21:43:16,131 - INFO - ✅ 代理关闭操作
2025-08-05 21:43:16,131 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:43:16,220 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:43:16,221 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:43:16,221 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 21:43:16,320 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:43:16,321 - INFO - ✅ 网络连接验证正常
2025-08-05 21:43:19,321 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 21:43:19,322 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 21:43:19,323 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 21:43:19,585 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 21:44:45,725 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 4 篇文章
2025-08-05 21:44:45,725 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250805_214445.xlsx
2025-08-05 21:44:45,725 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-05 21:45:00,726 - INFO - ============================================================
2025-08-05 21:45:00,726 - INFO - 📍 处理第 2/3 个公众号: 南京党建
2025-08-05 21:45:00,727 - INFO - ============================================================
2025-08-05 21:45:00,727 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 21:45:00,728 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:45:00,728 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:45:00,728 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:45:00,729 - INFO - === 开始重置网络状态 ===
2025-08-05 21:45:00,729 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:45:00,855 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:45:00,855 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:45:00,855 - INFO - 系统代理已成功关闭
2025-08-05 21:45:00,855 - INFO - ✅ 代理关闭操作
2025-08-05 21:45:00,855 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:45:00,935 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:45:00,936 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:45:00,936 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:45:00,936 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:45:00,937 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:45:01,484 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:45:01,485 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23496
2025-08-05 21:45:01,485 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:45:04,486 - INFO - 等待代理服务启动...
2025-08-05 21:45:04,487 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:45:04,584 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:45:04,585 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23496)
2025-08-05 21:45:04,585 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:45:04,585 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 21:45:04,586 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:45:04,586 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:45:04,587 - INFO - 正在查找微信主窗口...
2025-08-05 21:45:05,405 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:45:05,405 - INFO - 正在激活微信窗口...
2025-08-05 21:45:07,919 - INFO - 微信窗口已激活。
2025-08-05 21:45:07,920 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
